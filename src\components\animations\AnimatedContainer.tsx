import React from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';

export interface AnimatedContainerProps extends HTMLMotionProps<'div'> {
  children: React.ReactNode;
  animation?: 'fadeIn' | 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'scaleIn' | 'custom';
  delay?: number;
  duration?: number;
  stagger?: number;
}

const animations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  },
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 },
  },
  slideLeft: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  },
  slideRight: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 },
  },
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
  },
};

const AnimatedContainer: React.FC<AnimatedContainerProps> = ({
  children,
  animation = 'fadeIn',
  delay = 0,
  duration = 0.5,
  stagger = 0,
  initial,
  animate,
  exit,
  transition,
  ...props
}) => {
  const selectedAnimation = animation !== 'custom' ? animations[animation] : {};

  return (
    <motion.div
      initial={initial || selectedAnimation.initial}
      animate={animate || selectedAnimation.animate}
      exit={exit || selectedAnimation.exit}
      transition={{
        duration,
        delay,
        staggerChildren: stagger,
        ease: 'easeOut',
        ...transition,
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedContainer;
