import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../utils/cn';

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
};

const colorClasses = {
  primary: 'text-primary-500',
  secondary: 'text-secondary-500',
  white: 'text-white',
};

const SpinnerVariant: React.FC<{ size: string; color: string }> = ({ size, color }) => (
  <motion.div
    className={cn('border-2 border-current border-t-transparent rounded-full', size, color)}
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
  />
);

const DotsVariant: React.FC<{ color: string }> = ({ color }) => (
  <div className="flex space-x-1">
    {[0, 1, 2].map((i) => (
      <motion.div
        key={i}
        className={cn('w-2 h-2 rounded-full', color)}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.7, 1, 0.7],
        }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          delay: i * 0.2,
        }}
      />
    ))}
  </div>
);

const PulseVariant: React.FC<{ size: string; color: string }> = ({ size, color }) => (
  <motion.div
    className={cn('rounded-full', size, color)}
    animate={{
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
    }}
    transition={{
      duration: 1.5,
      repeat: Infinity,
      ease: 'easeInOut',
    }}
  />
);

const BarsVariant: React.FC<{ color: string }> = ({ color }) => (
  <div className="flex space-x-1">
    {[0, 1, 2, 3].map((i) => (
      <motion.div
        key={i}
        className={cn('w-1 h-6 rounded-full', color)}
        animate={{
          scaleY: [1, 2, 1],
        }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          delay: i * 0.1,
        }}
      />
    ))}
  </div>
);

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'spinner',
  color = 'primary',
  className,
}) => {
  const sizeClass = sizeClasses[size];
  const colorClass = colorClasses[color];

  const renderVariant = () => {
    switch (variant) {
      case 'spinner':
        return <SpinnerVariant size={sizeClass} color={colorClass} />;
      case 'dots':
        return <DotsVariant color={colorClass} />;
      case 'pulse':
        return <PulseVariant size={sizeClass} color={colorClass} />;
      case 'bars':
        return <BarsVariant color={colorClass} />;
      default:
        return <SpinnerVariant size={sizeClass} color={colorClass} />;
    }
  };

  return (
    <div className={cn('flex items-center justify-center', className)}>
      {renderVariant()}
    </div>
  );
};

export default LoadingSpinner;
