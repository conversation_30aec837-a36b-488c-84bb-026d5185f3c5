@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Custom CSS Variables for Theme */
:root {
  --color-primary: 59 130 246;
  --color-secondary: 139 92 246;
  --color-accent: 236 72 153;
  --color-success: 34 197 94;
  --color-warning: 251 191 36;
  --color-error: 239 68 68;
  
  --gradient-primary: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(139 92 246) 100%);
  --gradient-secondary: linear-gradient(135deg, rgb(139 92 246) 0%, rgb(236 72 153) 100%);
  --gradient-accent: linear-gradient(135deg, rgb(236 72 153) 0%, rgb(251 113 133) 100%);
  
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(59, 130, 246, 0.4);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(15 23 42);
}

::-webkit-scrollbar-thumb {
  background: rgb(71 85 105);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(100 116 139);
}

/* Focus Styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-dark-900;
}

/* Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

.glass {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.glass-light {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(203, 213, 225, 0.3);
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Shimmer Effect */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Image Gallery Overrides */
.image-gallery {
  @apply bg-transparent;
}

.image-gallery-slide img {
  @apply rounded-lg;
}

.image-gallery-thumbnails {
  @apply bg-dark-800/50 backdrop-blur-sm rounded-lg p-2;
}

/* Custom Button Variants */
.btn-primary {
  @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-dark-900 disabled:bg-dark-600 disabled:cursor-not-allowed transition-all duration-200;
}

.btn-secondary {
  @apply inline-flex items-center justify-center px-6 py-3 border border-dark-600 text-base font-medium rounded-lg shadow-sm text-dark-200 bg-dark-800 hover:bg-dark-700 hover:border-dark-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-dark-900 disabled:bg-dark-700 disabled:cursor-not-allowed transition-all duration-200;
}

.btn-ghost {
  @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-dark-300 hover:text-white hover:bg-dark-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-dark-900 disabled:text-dark-500 disabled:cursor-not-allowed transition-all duration-200;
}

/* Form Input Styles */
.input-primary {
  @apply block w-full px-3 py-2 border border-dark-600 rounded-lg shadow-sm bg-dark-800 text-dark-200 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
}

.textarea-primary {
  @apply block w-full px-3 py-2 border border-dark-600 rounded-lg shadow-sm bg-dark-800 text-dark-200 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 resize-y;
}

/* Card Styles */
.card {
  @apply bg-dark-800/50 backdrop-blur-sm border border-dark-700 rounded-xl shadow-lg;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-glow hover:border-dark-600 hover:-translate-y-1;
}

/* Responsive Design Helpers */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}
