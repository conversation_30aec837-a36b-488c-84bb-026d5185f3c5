import React from 'react';
import { cn } from '../../utils/cn';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

const Textarea: React.FC<TextareaProps> = ({
  label,
  error,
  helperText,
  resize = 'vertical',
  className,
  id,
  ...props
}) => {
  const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize',
  };

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={textareaId}
          className="block text-sm font-medium text-dark-300 mb-2"
        >
          {label}
        </label>
      )}
      
      <textarea
        {...props}
        id={textareaId}
        className={cn(
          'block w-full px-3 py-2 rounded-lg border transition-colors duration-200',
          'bg-dark-800 text-dark-200 placeholder-dark-400',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
          error
            ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
            : 'border-dark-600 hover:border-dark-500',
          resizeClasses[resize],
          className
        )}
      />
      
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-sm',
          error ? 'text-red-400' : 'text-dark-400'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  );
};

export default Textarea;
