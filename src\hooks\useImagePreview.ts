import { useState, useCallback } from 'react';

export interface ImagePreviewData {
  src: string;
  prompt: string;
  timestamp: number;
  aspectRatio: string;
}

/**
 * Custom hook for managing image preview modal state
 */
export function useImagePreview() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [images, setImages] = useState<ImagePreviewData[]>([]);

  const openPreview = useCallback((imageData: ImagePreviewData[], index: number = 0) => {
    setImages(imageData);
    setCurrentIndex(index);
    setIsOpen(true);
  }, []);

  const closePreview = useCallback(() => {
    setIsOpen(false);
    setCurrentIndex(0);
    setImages([]);
  }, []);

  const nextImage = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  }, [images.length]);

  const previousImage = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  const goToImage = useCallback((index: number) => {
    if (index >= 0 && index < images.length) {
      setCurrentIndex(index);
    }
  }, [images.length]);

  const currentImage = images[currentIndex] || null;

  return {
    isOpen,
    currentIndex,
    images,
    currentImage,
    openPreview,
    closePreview,
    nextImage,
    previousImage,
    goToImage,
  };
}
