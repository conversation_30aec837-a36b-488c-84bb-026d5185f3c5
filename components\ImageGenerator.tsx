
import React, { useState, useCallback } from 'react';
import { generateImages } from '../services/geminiService';
import type { AspectRatio } from '../types';
import { IMAGE_MODELS, ASPECT_RATIOS, IMAGE_COUNTS } from '../constants';
import ControlPanel from './ControlPanel';
import ImageCard from './ImageCard';
import ImageSkeleton from './ImageSkeleton';
import ImagePreviewModal from './ImagePreview/ImagePreviewModal';
import AnimatedContainer from './animations/AnimatedContainer';
import { useImagePreview, type ImagePreviewData } from '../hooks/useImagePreview';

const ImageGenerator: React.FC = () => {
    const [prompt, setPrompt] = useState<string>('A photorealistic image of a majestic lion in the savanna at sunset, with a dramatic sky.');
    const [numberOfImages, setNumberOfImages] = useState<number>(1);
    const [aspectRatio, setAspectRatio] = useState<AspectRatio>('16:9');
    const [model] = useState<string>(IMAGE_MODELS[0]);

    const [generatedImages, setGeneratedImages] = useState<ImagePreviewData[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // Image preview hook
    const {
        isOpen: isPreviewOpen,
        currentIndex,
        images: previewImages,
        openPreview,
        closePreview,
        nextImage,
        previousImage,
        goToImage,
    } = useImagePreview();

    const handleGenerate = useCallback(async () => {
        if (!prompt.trim()) {
            setError("Prompt cannot be empty.");
            return;
        }
        setIsLoading(true);
        setError(null);
        setGeneratedImages([]);

        try {
            const imageBytesArray = await generateImages({
                prompt,
                model,
                numberOfImages,
                aspectRatio,
            });

            const timestamp = Date.now();
            const imageData: ImagePreviewData[] = imageBytesArray.map((bytes, index) => ({
                src: `data:image/jpeg;base64,${bytes}`,
                prompt,
                timestamp: timestamp + index,
                aspectRatio,
            }));

            setGeneratedImages(imageData);
        } catch (err) {
            setError(err instanceof Error ? err.message : "An unexpected error occurred.");
            setGeneratedImages([]);
        } finally {
            setIsLoading(false);
        }
    }, [prompt, model, numberOfImages, aspectRatio]);

    const handleImagePreview = useCallback((index: number) => {
        openPreview(generatedImages, index);
    }, [generatedImages, openPreview]);
    
    return (
        <>
            <div className="flex flex-col gap-8">
                <AnimatedContainer animation="fadeIn" duration={0.6}>
                    <ControlPanel
                        prompt={prompt}
                        setPrompt={setPrompt}
                        numberOfImages={numberOfImages}
                        setNumberOfImages={setNumberOfImages}
                        aspectRatio={aspectRatio}
                        setAspectRatio={setAspectRatio}
                        imageModels={IMAGE_MODELS}
                        selectedModel={model}
                        aspectRatios={ASPECT_RATIOS}
                        imageCounts={IMAGE_COUNTS}
                        onGenerate={handleGenerate}
                        isLoading={isLoading}
                    />
                </AnimatedContainer>

                {error && (
                    <AnimatedContainer animation="slideUp" duration={0.4}>
                        <div className="bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-lg text-center">
                            <p><span className="font-bold">Error:</span> {error}</p>
                        </div>
                    </AnimatedContainer>
                )}

                {isLoading && (
                    <AnimatedContainer animation="fadeIn" duration={0.5}>
                        <div className="text-center text-dark-400 mb-6">
                            <h2 className="text-xl font-semibold text-dark-300">Generating masterpieces...</h2>
                            <p>This may take a moment, please be patient.</p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                            {Array.from({ length: numberOfImages }).map((_, index) => (
                                <ImageSkeleton key={index} aspectRatio={aspectRatio} />
                            ))}
                        </div>
                    </AnimatedContainer>
                )}

                {!isLoading && generatedImages.length === 0 && !error && (
                    <AnimatedContainer animation="fadeIn" duration={0.6}>
                        <div className="text-center py-16 text-dark-500 border-2 border-dashed border-dark-700 rounded-lg">
                            <h2 className="text-2xl font-semibold text-dark-400">Welcome to Imagen Studio</h2>
                            <p className="mt-2">Your generated images will appear here. Adjust the settings above and click 'Generate'.</p>
                        </div>
                    </AnimatedContainer>
                )}

                {!isLoading && generatedImages.length > 0 && (
                    <AnimatedContainer animation="fadeIn" duration={0.6} stagger={0.1}>
                        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                            {generatedImages.map((imageData, index) => (
                                <ImageCard
                                    key={index}
                                    src={imageData.src}
                                    prompt={imageData.prompt}
                                    aspectRatio={imageData.aspectRatio}
                                    timestamp={imageData.timestamp}
                                    onPreview={() => handleImagePreview(index)}
                                    index={index}
                                />
                            ))}
                        </div>
                    </AnimatedContainer>
                )}
            </div>

            {/* Image Preview Modal */}
            <ImagePreviewModal
                isOpen={isPreviewOpen}
                images={previewImages}
                currentIndex={currentIndex}
                onClose={closePreview}
                onNext={nextImage}
                onPrevious={previousImage}
                onGoToImage={goToImage}
            />
        </>
    );
};

export default ImageGenerator;