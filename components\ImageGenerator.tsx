
import React, { useState, useCallback } from 'react';
import { generateImages } from '../services/geminiService';
import type { AspectRatio } from '../types';
import { IMAGE_MODELS, ASPECT_RATIOS, IMAGE_COUNTS } from '../constants';
import ControlPanel from './ControlPanel';
import ImageCard from './ImageCard';
import ImageSkeleton from './ImageSkeleton';

const ImageGenerator: React.FC = () => {
    const [prompt, setPrompt] = useState<string>('A photorealistic image of a majestic lion in the savanna at sunset, with a dramatic sky.');
    const [numberOfImages, setNumberOfImages] = useState<number>(1);
    const [aspectRatio, setAspectRatio] = useState<AspectRatio>('16:9');
    const [model] = useState<string>(IMAGE_MODELS[0]);
    
    const [generatedImages, setGeneratedImages] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const handleGenerate = useCallback(async () => {
        if (!prompt.trim()) {
            setError("Prompt cannot be empty.");
            return;
        }
        setIsLoading(true);
        setError(null);
        setGeneratedImages([]);

        try {
            const imageBytesArray = await generateImages({
                prompt,
                model,
                numberOfImages,
                aspectRatio,
            });
            const base64Images = imageBytesArray.map(bytes => `data:image/jpeg;base64,${bytes}`);
            setGeneratedImages(base64Images);
        } catch (err) {
            setError(err instanceof Error ? err.message : "An unexpected error occurred.");
            setGeneratedImages([]);
        } finally {
            setIsLoading(false);
        }
    }, [prompt, model, numberOfImages, aspectRatio]);
    
    return (
        <div className="flex flex-col gap-8">
            <ControlPanel
                prompt={prompt}
                setPrompt={setPrompt}
                numberOfImages={numberOfImages}
                setNumberOfImages={setNumberOfImages}
                aspectRatio={aspectRatio}
                setAspectRatio={setAspectRatio}
                imageModels={IMAGE_MODELS}
                selectedModel={model}
                aspectRatios={ASPECT_RATIOS}
                imageCounts={IMAGE_COUNTS}
                onGenerate={handleGenerate}
                isLoading={isLoading}
            />

            {error && (
                <div className="bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-lg text-center">
                    <p><span className="font-bold">Error:</span> {error}</p>
                </div>
            )}

            {isLoading && (
                <div>
                    <div className="text-center text-slate-400 mb-6">
                        <h2 className="text-xl font-semibold text-slate-300">Generating masterpieces...</h2>
                        <p>This may take a moment, please be patient.</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                        {Array.from({ length: numberOfImages }).map((_, index) => (
                            <ImageSkeleton key={index} aspectRatio={aspectRatio} />
                        ))}
                    </div>
                </div>
            )}

            {!isLoading && generatedImages.length === 0 && !error && (
                 <div className="text-center py-16 text-slate-500 border-2 border-dashed border-slate-700 rounded-lg">
                    <h2 className="text-2xl font-semibold text-slate-400">Welcome to Imagen Studio</h2>
                    <p className="mt-2">Your generated images will appear here. Adjust the settings above and click 'Generate'.</p>
                </div>
            )}

            {!isLoading && generatedImages.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                    {generatedImages.map((imgSrc, index) => (
                        <ImageCard key={index} src={imgSrc} prompt={prompt} />
                    ))}
                </div>
            )}
        </div>
    );
};

export default ImageGenerator;