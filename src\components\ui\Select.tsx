import React from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '../../utils/cn';

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  helperText?: string;
  children: React.ReactNode;
}

const Select: React.FC<SelectProps> = ({
  label,
  error,
  helperText,
  children,
  className,
  id,
  ...props
}) => {
  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={selectId}
          className="block text-sm font-medium text-dark-300 mb-2"
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        <select
          {...props}
          id={selectId}
          className={cn(
            'block w-full px-3 py-2 pr-10 rounded-lg border transition-colors duration-200',
            'bg-dark-800 text-dark-200',
            'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'appearance-none cursor-pointer',
            error
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
              : 'border-dark-600 hover:border-dark-500',
            props.disabled && 'opacity-50 cursor-not-allowed',
            className
          )}
        >
          {children}
        </select>
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <ChevronDown className="h-4 w-4 text-dark-400" />
        </div>
      </div>
      
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-sm',
          error ? 'text-red-400' : 'text-dark-400'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  );
};

export default Select;
