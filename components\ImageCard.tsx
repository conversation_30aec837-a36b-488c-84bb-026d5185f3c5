
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Download, Eye, Share2 } from 'lucide-react';
import { cn } from '../utils/cn';
import type { AspectRatio } from '../types';

interface ImageCardProps {
  src: string;
  prompt: string;
  aspectRatio?: AspectRatio;
  timestamp?: number;
  onPreview?: () => void;
  index?: number;
}

const ImageCard: React.FC<ImageCardProps> = ({
  src,
  prompt,
  aspectRatio = '1:1',
  timestamp = Date.now(),
  onPreview,
  index = 0
}) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [isHovered, setIsHovered] = useState(false);

    useEffect(() => {
        const image = new Image();
        image.src = src;
        image.onload = () => {
            setIsLoaded(true);
        };
    }, [src]);


    const handleDownload = (e: React.MouseEvent) => {
        e.stopPropagation();
        const link = document.createElement('a');
        link.href = src;
        const filename = prompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
        link.download = `imagen-studio-${filename}-${timestamp}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleShare = async (e: React.MouseEvent) => {
        e.stopPropagation();
        if (navigator.share) {
            try {
                await navigator.share({
                    title: 'Generated Image',
                    text: prompt,
                    url: src,
                });
            } catch (error) {
                console.log('Error sharing:', error);
            }
        } else {
            try {
                await navigator.clipboard.writeText(src);
            } catch (error) {
                console.log('Error copying to clipboard:', error);
            }
        }
    };

    const handlePreview = () => {
        if (onPreview) {
            onPreview();
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{
                opacity: isLoaded ? 1 : 0,
                scale: isLoaded ? 1 : 0.9
            }}
            transition={{
                duration: 0.5,
                delay: index * 0.1,
                ease: 'easeOut'
            }}
            whileHover={{ y: -4 }}
            className={cn(
                'group relative overflow-hidden rounded-xl shadow-lg cursor-pointer',
                'bg-dark-800 border border-dark-700',
                'transition-all duration-300 hover:shadow-glow hover:border-dark-600'
            )}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={handlePreview}
        >
            <div className="relative aspect-square overflow-hidden">
                <img
                    src={src}
                    alt={prompt}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Action Buttons */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isHovered ? 1 : 0,
                        y: isHovered ? 0 : 20
                    }}
                    transition={{ duration: 0.2 }}
                    className="absolute inset-0 flex items-center justify-center"
                >
                    <div className="flex space-x-2">
                        <button
                            onClick={handlePreview}
                            className="p-3 bg-white/20 backdrop-blur-sm text-white rounded-full hover:bg-white/30 transition-colors"
                            title="Preview Image"
                        >
                            <Eye className="w-5 h-5" />
                        </button>

                        <button
                            onClick={handleDownload}
                            className="p-3 bg-white/20 backdrop-blur-sm text-white rounded-full hover:bg-white/30 transition-colors"
                            title="Download Image"
                        >
                            <Download className="w-5 h-5" />
                        </button>

                        <button
                            onClick={handleShare}
                            className="p-3 bg-white/20 backdrop-blur-sm text-white rounded-full hover:bg-white/30 transition-colors"
                            title="Share Image"
                        >
                            <Share2 className="w-5 h-5" />
                        </button>
                    </div>
                </motion.div>
            </div>

            {/* Image Info */}
            <div className="p-4">
                <p className="text-dark-200 text-sm font-medium leading-tight line-clamp-2">
                    {prompt.length > 60 ? `${prompt.substring(0, 60)}...` : prompt}
                </p>
                <div className="flex justify-between items-center mt-2 text-xs text-dark-400">
                    <span>{aspectRatio}</span>
                    <span>{new Date(timestamp).toLocaleDateString()}</span>
                </div>
            </div>
        </motion.div>
    );
};

export default ImageCard;