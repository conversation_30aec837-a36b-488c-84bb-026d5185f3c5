
import React, { useState, useEffect } from 'react';

interface ImageCardProps {
  src: string;
  prompt: string;
}

const DownloadIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
    </svg>
);

const ImageCard: React.FC<ImageCardProps> = ({ src, prompt }) => {
    const [isLoaded, setIsLoaded] = useState(false);

    useEffect(() => {
        const image = new Image();
        image.src = src;
        image.onload = () => {
            setIsLoaded(true);
        };
    }, [src]);


    const handleDownload = () => {
        const link = document.createElement('a');
        link.href = src;
        // Sanitize prompt for filename
        const filename = prompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
        link.download = `imagen-studio-${filename}-${Date.now()}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <div className={`group relative overflow-hidden rounded-lg shadow-lg bg-slate-800 border border-slate-700 transition-all duration-500 ease-out hover:shadow-indigo-500/30 hover:border-slate-600 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
            <img src={src} alt={prompt} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
            <div className="absolute bottom-0 left-0 right-0 p-4 flex justify-between items-end">
                <p className="text-white text-sm font-medium leading-tight opacity-0 group-hover:opacity-100 transition-opacity duration-300 delay-100">{prompt.substring(0, 80)}{prompt.length > 80 ? '...' : ''}</p>
                <button 
                    onClick={handleDownload}
                    className="flex-shrink-0 bg-white/20 text-white backdrop-blur-sm p-2 rounded-full hover:bg-white/40 transition-all duration-300 scale-0 group-hover:scale-100 ease-in-out"
                    aria-label="Download Image"
                    title="Download Image"
                >
                   <DownloadIcon />
                </button>
            </div>
        </div>
    );
};

export default ImageCard;