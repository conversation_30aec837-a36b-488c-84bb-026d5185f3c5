import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function to merge Tailwind CSS classes with clsx
 * Handles conditional classes and removes conflicts
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Utility function to create responsive class names
 */
export function responsive(base: string, sm?: string, md?: string, lg?: string, xl?: string) {
  return cn(
    base,
    sm && `sm:${sm}`,
    md && `md:${md}`,
    lg && `lg:${lg}`,
    xl && `xl:${xl}`
  );
}

/**
 * Utility function for conditional styling
 */
export function conditional(condition: boolean, trueClasses: string, falseClasses?: string) {
  return condition ? trueClasses : falseClasses || '';
}
