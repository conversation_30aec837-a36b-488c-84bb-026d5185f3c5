
import React from 'react';
import { motion } from 'framer-motion';
import Header from './components/Header';
import ImageGenerator from './components/ImageGenerator';
import AnimatedContainer from './components/animations/AnimatedContainer';

const App: React.FC = () => {
  return (
    <div className="min-h-screen text-dark-200 bg-dark-900 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(59,130,246,0.1),rgba(255,255,255,0))]">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <AnimatedContainer animation="fadeIn" duration={0.8} delay={0.2}>
          <ImageGenerator />
        </AnimatedContainer>
      </main>

      <motion.footer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1 }}
        className="text-center py-8 mt-16 border-t border-dark-800"
      >
        <div className="container mx-auto px-4">
          <p className="text-dark-500 text-sm mb-2">
            Powered by Google Gemini & React
          </p>
          <p className="text-dark-600 text-xs">
            Designed for modern creative workflows
          </p>
        </div>
      </motion.footer>
    </div>
  );
};

export default App;