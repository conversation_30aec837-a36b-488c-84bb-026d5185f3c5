
import React from 'react';
import Header from './components/Header';
import ImageGenerator from './components/ImageGenerator';

const App: React.FC = () => {
  return (
    <div className="min-h-screen text-slate-200">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <ImageGenerator />
      </main>
      <footer className="text-center py-4 mt-8 text-slate-500 text-sm transition-opacity duration-300 hover:opacity-100">
        <p>Powered by Google Gemini & React. Designed for modern creative workflows.</p>
      </footer>
    </div>
  );
};

export default App;