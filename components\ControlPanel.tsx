
import React from 'react';
import type { AspectRatio } from '../types';
import Button from './Button';
import Select from './Select';

interface ControlPanelProps {
    prompt: string;
    setPrompt: (prompt: string) => void;
    numberOfImages: number;
    setNumberOfImages: (count: number) => void;
    aspectRatio: AspectRatio;
    setAspectRatio: (ratio: AspectRatio) => void;
    imageModels: string[];
    selectedModel: string;
    aspectRatios: { value: AspectRatio, label: string }[];
    imageCounts: number[];
    onGenerate: () => void;
    isLoading: boolean;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
    prompt,
    setPrompt,
    numberOfImages,
    setNumberOfImages,
    aspectRatio,
    setAspectRatio,
    imageModels,
    selectedModel,
    aspectRatios,
    imageCounts,
    onGenerate,
    isLoading
}) => {
    return (
        <div className="bg-slate-800/50 p-6 rounded-xl border border-slate-700 shadow-lg flex flex-col gap-6">
            <div className="flex flex-col lg:flex-row gap-4">
                <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Enter your creative prompt here..."
                    className="flex-grow bg-slate-900 border border-slate-600 rounded-lg p-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-600 focus:outline-none transition-all duration-200 resize-y min-h-[80px] text-slate-200"
                />
                <Button onClick={onGenerate} disabled={isLoading}>
                    {isLoading ? 'Generating...' : 'Generate'}
                </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex flex-col gap-2">
                    <label htmlFor="model-select" className="text-sm font-medium text-slate-400">Model</label>
                    <Select
                        id="model-select"
                        value={selectedModel}
                        onChange={() => {}} // Model is not changeable for now
                        disabled={true}
                    >
                        {imageModels.map(model => <option key={model} value={model}>{model}</option>)}
                    </Select>
                </div>
                <div className="flex flex-col gap-2">
                    <label htmlFor="aspect-ratio-select" className="text-sm font-medium text-slate-400">Aspect Ratio</label>
                    <Select
                        id="aspect-ratio-select"
                        value={aspectRatio}
                        onChange={(e) => setAspectRatio(e.target.value as AspectRatio)}
                        disabled={isLoading}
                    >
                        {aspectRatios.map(ratio => <option key={ratio.value} value={ratio.value}>{ratio.label}</option>)}
                    </Select>
                </div>
                <div className="flex flex-col gap-2">
                     <label className="text-sm font-medium text-slate-400">Number of Images</label>
                     <div className="grid grid-cols-4 gap-2 bg-slate-900 p-1 rounded-lg border border-slate-600">
                        {imageCounts.map(count => (
                             <button
                                key={count}
                                onClick={() => setNumberOfImages(count)}
                                disabled={isLoading}
                                className={`px-3 py-1.5 rounded-md text-sm font-semibold transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-indigo-500 ${
                                    numberOfImages === count 
                                    ? 'bg-indigo-600 text-white shadow-sm' 
                                    : 'bg-transparent text-slate-300 hover:bg-slate-700'
                                }`}
                            >
                                {count}
                            </button>
                        ))}
                     </div>
                </div>
            </div>
        </div>
    );
}

export default ControlPanel;