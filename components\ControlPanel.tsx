
import React from 'react';
import type { AspectRatio } from '../types';
import Button from './ui/Button';
import Select from './ui/Select';
import Textarea from './ui/Textarea';
import Card from './ui/Card';
import { Sparkles } from 'lucide-react';

interface ControlPanelProps {
    prompt: string;
    setPrompt: (prompt: string) => void;
    numberOfImages: number;
    setNumberOfImages: (count: number) => void;
    aspectRatio: AspectRatio;
    setAspectRatio: (ratio: AspectRatio) => void;
    imageModels: string[];
    selectedModel: string;
    aspectRatios: { value: AspectRatio, label: string }[];
    imageCounts: number[];
    onGenerate: () => void;
    isLoading: boolean;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
    prompt,
    setPrompt,
    numberOfImages,
    setNumberOfImages,
    aspectRatio,
    setAspectRatio,
    imageModels,
    selectedModel,
    aspectRatios,
    imageCounts,
    onGenerate,
    isLoading
}) => {
    return (
        <Card variant="glass" padding="lg" className="shadow-lg">
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-grow">
                    <Textarea
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        placeholder="Enter your creative prompt here..."
                        className="min-h-[80px]"
                        label="Image Prompt"
                    />
                </div>
                <div className="flex items-end">
                    <Button
                        onClick={onGenerate}
                        disabled={isLoading}
                        loading={isLoading}
                        variant="gradient"
                        size="lg"
                        icon={<Sparkles className="w-5 h-5" />}
                        className="h-fit"
                    >
                        {isLoading ? 'Generating...' : 'Generate'}
                    </Button>
                </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Select
                    label="Model"
                    value={selectedModel}
                    onChange={() => {}} // Model is not changeable for now
                    disabled={true}
                >
                    {imageModels.map(model => <option key={model} value={model}>{model}</option>)}
                </Select>

                <Select
                    label="Aspect Ratio"
                    value={aspectRatio}
                    onChange={(e) => setAspectRatio(e.target.value as AspectRatio)}
                    disabled={isLoading}
                >
                    {aspectRatios.map(ratio => <option key={ratio.value} value={ratio.value}>{ratio.label}</option>)}
                </Select>

                <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium text-dark-300">Number of Images</label>
                    <div className="grid grid-cols-4 gap-2 bg-dark-900 p-1 rounded-lg border border-dark-600">
                        {imageCounts.map(count => (
                            <button
                                key={count}
                                onClick={() => setNumberOfImages(count)}
                                disabled={isLoading}
                                className={`px-3 py-1.5 rounded-md text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-800 focus:ring-primary-500 ${
                                    numberOfImages === count
                                    ? 'bg-primary-600 text-white shadow-sm transform scale-105'
                                    : 'bg-transparent text-dark-300 hover:bg-dark-700 hover:text-white'
                                }`}
                            >
                                {count}
                            </button>
                        ))}
                    </div>
                </div>
            </div>
        </Card>
    );
}

export default ControlPanel;