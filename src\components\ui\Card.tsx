import React from 'react';
import { cn } from '../../utils/cn';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: 'default' | 'glass' | 'gradient' | 'elevated';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
}

const cardVariants = {
  default: 'bg-dark-800/50 border border-dark-700',
  glass: 'glass',
  gradient: 'bg-gradient-to-br from-dark-800/80 to-dark-900/80 border border-dark-600/50',
  elevated: 'bg-dark-800 border border-dark-700 shadow-2xl',
};

const cardPadding = {
  none: '',
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
  xl: 'p-8',
};

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  hover = false,
  className,
  ...props
}) => {
  return (
    <div
      {...props}
      className={cn(
        'rounded-xl transition-all duration-300',
        cardVariants[variant],
        cardPadding[padding],
        hover && 'hover:shadow-glow hover:border-dark-600 hover:-translate-y-1 cursor-pointer',
        className
      )}
    >
      {children}
    </div>
  );
};

export default Card;
