
import React from 'react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
    children: React.ReactNode;
}

const Select: React.FC<SelectProps> = ({ children, ...props }) => {
    return (
        <div className="relative">
            <select
                {...props}
                className="w-full appearance-none bg-slate-900 border border-slate-600 rounded-lg py-2.5 px-3 focus:ring-2 focus:ring-indigo-500 focus:outline-none transition duration-200 text-slate-200 disabled:bg-slate-700 disabled:cursor-not-allowed"
            >
                {children}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-slate-400">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
            </div>
        </div>
    );
};

export default Select;
