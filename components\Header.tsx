
import React from 'react';

const Header: React.FC = () => {
    const Logo = () => (
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-400"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-500"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-600"/>
        </svg>
    );

    return (
        <header className="bg-slate-900/70 backdrop-blur-lg sticky top-0 z-10 border-b border-slate-700/50">
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-between h-16">
                    <div className="flex items-center space-x-3 group cursor-pointer">
                         <div className="transition-all duration-300 group-hover:drop-shadow-[0_0_8px_rgba(129,140,248,0.7)]">
                            <Logo />
                        </div>
                        <h1 className="text-xl font-bold text-slate-100 transition-colors group-hover:text-indigo-300">Imagen Studio</h1>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;