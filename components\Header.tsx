
import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, Palette } from 'lucide-react';

const Header: React.FC = () => {
    const Logo = () => (
        <motion.div
            whileHover={{ rotate: 360 }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
            className="relative"
        >
            <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg flex items-center justify-center">
                <Palette className="w-5 h-5 text-white" />
            </div>
            <motion.div
                animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                }}
                transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                }}
                className="absolute inset-0 bg-gradient-to-br from-primary-400 to-secondary-500 rounded-lg blur-sm -z-10"
            />
        </motion.div>
    );

    return (
        <motion.header
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6 }}
            className="glass sticky top-0 z-50 border-b border-dark-700/50"
        >
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-between h-16">
                    <motion.div
                        className="flex items-center space-x-3 group cursor-pointer"
                        whileHover={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                    >
                        <Logo />
                        <div>
                            <h1 className="text-xl font-bold text-gradient">
                                Imagen Studio
                            </h1>
                            <p className="text-xs text-dark-400 -mt-1">
                                AI-Powered Image Generation
                            </p>
                        </div>
                    </motion.div>

                    <div className="flex items-center space-x-2">
                        <motion.div
                            animate={{
                                rotate: [0, 360],
                            }}
                            transition={{
                                duration: 8,
                                repeat: Infinity,
                                ease: "linear"
                            }}
                        >
                            <Sparkles className="w-5 h-5 text-primary-400" />
                        </motion.div>
                        <span className="text-sm text-dark-400 hidden sm:block">
                            Powered by Google Gemini
                        </span>
                    </div>
                </div>
            </div>
        </motion.header>
    );
};

export default Header;