import React from 'react';
import { cn } from '../../utils/cn';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  icon,
  iconPosition = 'left',
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-dark-300 mb-2"
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        {icon && iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-dark-400">{icon}</span>
          </div>
        )}
        
        <input
          {...props}
          id={inputId}
          className={cn(
            'block w-full rounded-lg border transition-colors duration-200',
            'bg-dark-800 text-dark-200 placeholder-dark-400',
            'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            error
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
              : 'border-dark-600 hover:border-dark-500',
            icon && iconPosition === 'left' ? 'pl-10 pr-3 py-2' : 'px-3 py-2',
            icon && iconPosition === 'right' ? 'pr-10 pl-3 py-2' : 'px-3 py-2',
            className
          )}
        />
        
        {icon && iconPosition === 'right' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-dark-400">{icon}</span>
          </div>
        )}
      </div>
      
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-sm',
          error ? 'text-red-400' : 'text-dark-400'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  );
};

export default Input;
