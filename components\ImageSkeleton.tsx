
import React from 'react';
import type { AspectRatio } from '../types';

interface ImageSkeletonProps {
    aspectRatio: AspectRatio;
}

const getAspectRatioClass = (ratio: AspectRatio): string => {
    switch (ratio) {
        case '16:9': return 'aspect-[16/9]';
        case '9:16': return 'aspect-[9/16]';
        case '4:3': return 'aspect-[4/3]';
        case '3:4': return 'aspect-[3/4]';
        case '1:1':
        default:
            return 'aspect-square';
    }
}

const ImageSkeleton: React.FC<ImageSkeletonProps> = ({ aspectRatio }) => {
    const aspectRatioClass = getAspectRatioClass(aspectRatio);

    return (
        <div className={`w-full bg-slate-800 border border-slate-700 rounded-lg animate-pulse ${aspectRatioClass}`}>
            <div className="w-full h-full bg-slate-700/50 rounded-md"></div>
        </div>
    );
};

export default ImageSkeleton;