import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../utils/cn';

export interface ProgressBarProps {
  progress: number; // 0-100
  variant?: 'default' | 'gradient' | 'striped';
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  animated?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: 'h-1',
  md: 'h-2',
  lg: 'h-3',
};

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  variant = 'default',
  size = 'md',
  showPercentage = false,
  animated = true,
  className,
}) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));

  const getBarClasses = () => {
    const baseClasses = 'h-full rounded-full transition-all duration-500';
    
    switch (variant) {
      case 'gradient':
        return cn(baseClasses, 'bg-gradient-to-r from-primary-500 to-secondary-500');
      case 'striped':
        return cn(
          baseClasses,
          'bg-primary-500',
          animated && 'bg-gradient-to-r from-primary-500 via-primary-400 to-primary-500 bg-[length:20px_20px] animate-pulse'
        );
      default:
        return cn(baseClasses, 'bg-primary-500');
    }
  };

  return (
    <div className={cn('w-full', className)}>
      {showPercentage && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-dark-300">Progress</span>
          <span className="text-sm font-medium text-dark-200">
            {Math.round(clampedProgress)}%
          </span>
        </div>
      )}
      
      <div className={cn(
        'w-full bg-dark-700 rounded-full overflow-hidden',
        sizeClasses[size]
      )}>
        <motion.div
          className={getBarClasses()}
          initial={{ width: 0 }}
          animate={{ width: `${clampedProgress}%` }}
          transition={{
            duration: 0.5,
            ease: 'easeOut',
          }}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
