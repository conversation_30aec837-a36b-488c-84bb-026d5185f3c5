import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Grid, 
  List, 
  Filter, 
  SortAsc, 
  SortDesc, 
  Download, 
  Trash2, 
  CheckSquare, 
  Square,
  Search
} from 'lucide-react';
import { cn } from '../../utils/cn';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Select from '../ui/Select';
import Card from '../ui/Card';
import ImageCard from '../ImageCard';
import AnimatedContainer from '../animations/AnimatedContainer';
import type { ImagePreviewData } from '../../hooks/useImagePreview';

export interface ImageGalleryProps {
  images: ImagePreviewData[];
  onImagePreview: (index: number) => void;
  onImagesDelete?: (indices: number[]) => void;
  onBatchDownload?: (indices: number[]) => void;
}

type ViewMode = 'grid' | 'list';
type SortBy = 'date' | 'prompt' | 'aspectRatio';
type SortOrder = 'asc' | 'desc';

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  onImagePreview,
  onImagesDelete,
  onBatchDownload,
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortBy>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [selectedImages, setSelectedImages] = useState<Set<number>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [aspectRatioFilter, setAspectRatioFilter] = useState<string>('all');

  // Filter and sort images
  const filteredAndSortedImages = useMemo(() => {
    let filtered = images.filter((image, index) => {
      const matchesSearch = image.prompt.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesAspectRatio = aspectRatioFilter === 'all' || image.aspectRatio === aspectRatioFilter;
      return matchesSearch && matchesAspectRatio;
    });

    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = a.timestamp - b.timestamp;
          break;
        case 'prompt':
          comparison = a.prompt.localeCompare(b.prompt);
          break;
        case 'aspectRatio':
          comparison = a.aspectRatio.localeCompare(b.aspectRatio);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered.map((image, originalIndex) => ({
      ...image,
      originalIndex: images.indexOf(image)
    }));
  }, [images, searchQuery, sortBy, sortOrder, aspectRatioFilter]);

  // Selection handlers
  const toggleImageSelection = (index: number) => {
    const newSelected = new Set(selectedImages);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedImages(newSelected);
  };

  const selectAll = () => {
    setSelectedImages(new Set(filteredAndSortedImages.map((_, index) => index)));
  };

  const clearSelection = () => {
    setSelectedImages(new Set());
  };

  const handleBatchDownload = () => {
    if (onBatchDownload && selectedImages.size > 0) {
      const indices = Array.from(selectedImages);
      onBatchDownload(indices);
    }
  };

  const handleBatchDelete = () => {
    if (onImagesDelete && selectedImages.size > 0) {
      const indices = Array.from(selectedImages);
      onImagesDelete(indices);
      clearSelection();
    }
  };

  // Get unique aspect ratios for filter
  const uniqueAspectRatios = useMemo(() => {
    const ratios = [...new Set(images.map(img => img.aspectRatio))];
    return ratios.sort();
  }, [images]);

  const gridCols = viewMode === 'grid' 
    ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5' 
    : 'grid-cols-1';

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <Card variant="glass" padding="md">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <Input
              placeholder="Search images by prompt..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={<Search className="w-4 h-4" />}
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              icon={<Grid className="w-4 h-4" />}
            />
            <Button
              variant={viewMode === 'list' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              icon={<List className="w-4 h-4" />}
            />
          </div>

          {/* Filter Toggle */}
          <Button
            variant={showFilters ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            icon={<Filter className="w-4 h-4" />}
          >
            Filters
          </Button>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="pt-4 border-t border-dark-700 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Select
                    label="Sort By"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as SortBy)}
                  >
                    <option value="date">Date Created</option>
                    <option value="prompt">Prompt</option>
                    <option value="aspectRatio">Aspect Ratio</option>
                  </Select>

                  <Select
                    label="Sort Order"
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as SortOrder)}
                  >
                    <option value="desc">Descending</option>
                    <option value="asc">Ascending</option>
                  </Select>

                  <Select
                    label="Aspect Ratio"
                    value={aspectRatioFilter}
                    onChange={(e) => setAspectRatioFilter(e.target.value)}
                  >
                    <option value="all">All Ratios</option>
                    {uniqueAspectRatios.map(ratio => (
                      <option key={ratio} value={ratio}>{ratio}</option>
                    ))}
                  </Select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* Batch Actions */}
      {selectedImages.size > 0 && (
        <AnimatedContainer animation="slideUp" duration={0.3}>
          <Card variant="gradient" padding="md">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-white font-medium">
                  {selectedImages.size} image{selectedImages.size !== 1 ? 's' : ''} selected
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSelection}
                >
                  Clear Selection
                </Button>
              </div>
              
              <div className="flex items-center space-x-2">
                {onBatchDownload && (
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleBatchDownload}
                    icon={<Download className="w-4 h-4" />}
                  >
                    Download
                  </Button>
                )}
                
                {onImagesDelete && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBatchDelete}
                    icon={<Trash2 className="w-4 h-4" />}
                    className="text-red-400 border-red-400 hover:bg-red-500 hover:text-white"
                  >
                    Delete
                  </Button>
                )}
              </div>
            </div>
          </Card>
        </AnimatedContainer>
      )}

      {/* Results Info */}
      <div className="flex items-center justify-between text-sm text-dark-400">
        <span>
          Showing {filteredAndSortedImages.length} of {images.length} images
        </span>
        
        {filteredAndSortedImages.length > 0 && (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={selectedImages.size === filteredAndSortedImages.length ? clearSelection : selectAll}
              icon={selectedImages.size === filteredAndSortedImages.length ? <Square className="w-4 h-4" /> : <CheckSquare className="w-4 h-4" />}
            >
              {selectedImages.size === filteredAndSortedImages.length ? 'Deselect All' : 'Select All'}
            </Button>
          </div>
        )}
      </div>

      {/* Image Grid */}
      {filteredAndSortedImages.length > 0 ? (
        <AnimatedContainer animation="fadeIn" duration={0.6} stagger={0.05}>
          <div className={cn('grid gap-6', gridCols)}>
            {filteredAndSortedImages.map((imageData, index) => (
              <motion.div
                key={`${imageData.originalIndex}-${imageData.timestamp}`}
                layout
                className="relative"
              >
                {/* Selection Checkbox */}
                <motion.button
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute top-2 left-2 z-10 p-1 bg-black/50 backdrop-blur-sm rounded-md text-white hover:bg-black/70 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleImageSelection(index);
                  }}
                >
                  {selectedImages.has(index) ? (
                    <CheckSquare className="w-4 h-4" />
                  ) : (
                    <Square className="w-4 h-4" />
                  )}
                </motion.button>

                <ImageCard
                  src={imageData.src}
                  prompt={imageData.prompt}
                  aspectRatio={imageData.aspectRatio}
                  timestamp={imageData.timestamp}
                  onPreview={() => onImagePreview(imageData.originalIndex)}
                  index={index}
                />
              </motion.div>
            ))}
          </div>
        </AnimatedContainer>
      ) : (
        <AnimatedContainer animation="fadeIn" duration={0.6}>
          <Card variant="default" padding="xl" className="text-center">
            <div className="text-dark-500">
              <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium text-dark-400 mb-2">No images found</h3>
              <p className="text-sm">
                {searchQuery || aspectRatioFilter !== 'all' 
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Generate some images to get started!'
                }
              </p>
            </div>
          </Card>
        </AnimatedContainer>
      )}
    </div>
  );
};

export default ImageGallery;
