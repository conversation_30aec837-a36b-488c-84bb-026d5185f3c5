
import { GoogleGenAI } from "@google/genai";
import type { ImageGenerationOptions } from '../types';

if (!process.env.API_KEY) {
    throw new Error("API_KEY environment variable is not set");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

export const generateImages = async (options: ImageGenerationOptions): Promise<string[]> => {
    try {
        const response = await ai.models.generateImages({
            model: options.model,
            prompt: options.prompt,
            config: {
                numberOfImages: options.numberOfImages,
                outputMimeType: 'image/jpeg',
                aspectRatio: options.aspectRatio,
            },
        });

        if (!response.generatedImages || response.generatedImages.length === 0) {
            throw new Error("API returned no images.");
        }

        return response.generatedImages.map(img => {
            if (!img.image.imageBytes) {
                throw new Error("Received an empty image response from the API.");
            }
            return img.image.imageBytes;
        });

    } catch (error) {
        console.error("Error generating images:", error);
        if (error instanceof Error) {
            throw new Error(`Failed to generate images: ${error.message}`);
        }
        throw new Error("An unknown error occurred during image generation.");
    }
};
