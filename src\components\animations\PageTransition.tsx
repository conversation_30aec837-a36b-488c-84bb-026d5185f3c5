import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export interface PageTransitionProps {
  children: React.ReactNode;
  mode?: 'wait' | 'sync' | 'popLayout';
  transition?: 'fade' | 'slide' | 'scale' | 'custom';
  duration?: number;
  custom?: {
    initial?: any;
    animate?: any;
    exit?: any;
  };
}

const transitions = {
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  slide: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  },
  scale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.05 },
  },
};

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  mode = 'wait',
  transition = 'fade',
  duration = 0.3,
  custom,
}) => {
  const selectedTransition = transition !== 'custom' ? transitions[transition] : custom;

  return (
    <AnimatePresence mode={mode}>
      <motion.div
        initial={selectedTransition?.initial}
        animate={selectedTransition?.animate}
        exit={selectedTransition?.exit}
        transition={{
          duration,
          ease: 'easeInOut',
        }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

export default PageTransition;
