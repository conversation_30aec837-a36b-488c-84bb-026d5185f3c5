
import { AspectRatio } from './types';

export const IMAGE_MODELS: string[] = ['imagen-3.0-generate-002'];

export const ASPECT_RATIOS: { value: AspectRatio; label: string }[] = [
  { value: '1:1', label: 'Square (1:1)' },
  { value: '16:9', label: 'Widescreen (16:9)' },
  { value: '9:16', label: 'Portrait (9:16)' },
  { value: '4:3', label: 'Landscape (4:3)' },
  { value: '3:4', label: 'Tall (3:4)' },
];

export const IMAGE_COUNTS: number[] = [1, 2, 3, 4];
